<script setup lang="tsx" generic="T extends AnyObject">
import type { AnyObject, ColumnDefinition, RowAction } from '@/types';
import type { FunctionalComponent } from 'vue';
import type { CheckboxValueType, RowEventHandlers } from 'element-plus';
import { ElCheckbox, TableV2FixedDir, TableV2SortOrder, type SortBy } from 'element-plus';
import { computed, shallowRef, useTemplateRef } from 'vue';
import { useFilteredData } from '@/composables/useFilteredData';
import { Search } from '@element-plus/icons-vue';
import TableRowButton from './TableRowButton.vue';
import type { RiskRule } from '../../../../xtrade-sdk/dist';

const {
  columns,
  data,
  select,
  identity = 'id',
  sort = { key: '', order: TableV2SortOrder.DESC },
  rowActions = [],
  rowActionWidth = 80,
  headerRowHeight = 44,
  rowHeight = 44,
  fixed = false,
  showIndex = false,
  searchPlaceholder = '搜索',
  customFilter,
} = defineProps<{
  columns: ColumnDefinition<T>;
  data: T[];
  /** 是否显示选择列 */
  select?: boolean;
  /** 行数据的唯一标识字段 */
  identity?: keyof T;
  /** 默认排序列 */
  sort?: SortBy;
  /** 行操作 */
  rowActions?: RowAction<T>[];
  /** 行操作列宽度 */
  rowActionWidth?: number;
  /** 标题行高 */
  headerRowHeight?: number;
  /** 行高 */
  rowHeight?: number;
  /** 单元格宽度是自适应还是固定 */
  fixed?: boolean;
  /** 是否显示序号列 */
  showIndex?: boolean;
  /** 搜索框占位符 */
  searchPlaceholder?: string;
  /** 自定义过滤 */
  customFilter?: (item: T) => boolean;
}>();

const emit = defineEmits<{
  'row-dblclick': [rowData: T];
  'row-click': [rowData: T];
}>();

const rowEventHandlers: RowEventHandlers = {
  onClick: ({ rowData }) => {
    clickedRow.value = rowData;
    emit('row-click', rowData);
  },
  onDblclick: ({ rowData }) => {
    emit('row-dblclick', rowData);
  },
};

// 默认排序状态
const sortState = shallowRef<SortBy>({
  key: sort.key,
  order: sort.order,
});

type SelectionCellProps = {
  value: boolean;
  intermediate?: boolean;
  onChange: (value: CheckboxValueType) => void;
};

// 单击选中的行
const clickedRow = shallowRef<T | null>(null);

// 过滤，增加选择列，排序，最终获得的列表数组
const { filteredData, query, selectedRowsMap } = useFilteredData(() => data, {
  customFilter,
  identity,
  select,
  sortState,
});
// 已选择的行
const selectedRows = computed<T[]>(() => {
  if (!select) return [];
  return data.filter(row => {
    const key = row[identity] as string | number;
    return selectedRowsMap.value[key];
  });
});

const totalRecords = computed(() => filteredData.value.length);

// 添加选择列和操作列
const tableColumns = computed(() => {
  const fullColumns: ColumnDefinition<T> = [...columns];

  // 字段补齐
  fullColumns.forEach(col => {
    if (!col.dataKey) {
      col.dataKey = col.key;
    }
  });

  if (showIndex) {
    const indexColumn: ColumnDefinition<T>[0] = {
      key: 'index',
      title: '序号',
      width: 60,
      cellRenderer: ({ rowIndex }) => <span>{rowIndex + 1}</span>,
    };
    fullColumns.unshift(indexColumn);
  }

  if (select) {
    const selectionColumn: ColumnDefinition<T>[0] = {
      key: 'selection',
      title: '',
      align: 'center',
      width: 50,
      fixed: TableV2FixedDir.LEFT,
      cellRenderer: ({ rowData }: { rowData: T }) => {
        return (
          <SelectionCell
            value={selectedRowsMap.value[rowData[identity]]}
            onChange={val => {
              const key = rowData[identity] as string | number;
              selectedRowsMap.value[key] = val as boolean;
            }}
          />
        );
      },
      headerCellRenderer: () => {
        const onChange = (value: CheckboxValueType) => {
          filteredData.value.forEach(row => {
            const key = row[identity] as string | number;
            selectedRowsMap.value[key] = value as boolean;
          });
        };
        const allSelected = filteredData.value.every(row => row.checked);
        const containsChecked = filteredData.value.some(row => row.checked);
        return (
          <SelectionCell
            value={allSelected}
            intermediate={containsChecked && !allSelected}
            onChange={onChange}
          />
        );
      },
    };
    fullColumns.unshift(selectionColumn);
  }

  if (rowActions.length) {
    const actionsColumn: ColumnDefinition<T>[0] = {
      key: 'actions',
      title: '操作',
      fixed: TableV2FixedDir.RIGHT,
      width: rowActionWidth,
      cellRenderer: ({ rowData }: { rowData: T }) => {
        return (
          <div class="flex aic">
            {rowActions.map(action => {
              if (action.show && !action.show(rowData)) {
                return null;
              } else {
                return <TableRowButton action={action} rowData={rowData} />;
              }
            })}
          </div>
        );
      },
    };
    fullColumns.push(actionsColumn);
  }

  return fullColumns;
});

const SelectionCell: FunctionalComponent<SelectionCellProps> = ({
  value,
  intermediate = false,
  onChange,
}) => {
  return <ElCheckbox onChange={onChange} modelValue={value} indeterminate={intermediate} />;
};

const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  if (clickedRow.value) {
    const key = clickedRow.value[identity] as string | number;
    if (filteredData.value[rowIndex][identity] === key) {
      return 'important-bg-[var(--g-active-row)]';
    }
  }
  return rowIndex % 2 === 0 ? 'bg-[var(--g-table-odd-row-bg)]' : 'bg-[var(--g-table-even-row-bg)]';
};

const $eltable2 = useTemplateRef('eltable2') as any;

const onSort = (sortBy: SortBy) => {
  sortState.value = sortBy;
};

function setCurrentRow(rule: RiskRule) {
  $eltable2.value.setCurrentRow(rule);
}

function clearSelection() {
  $eltable2.value.clearSelection();
}

defineExpose({
  setCurrentRow,
  clearSelection,
  selectedRows,
});
</script>
<template>
  <div flex="~ col" w-full h-full>
    <div class="toolbar" h-57 px-15 py-8 flex aic jcsb bg="[--g-bg]">
      <div flex aic>
        <el-input
          :prefix-icon="Search"
          class="typical-search-box w-270! mr-10"
          :placeholder="searchPlaceholder"
          v-model="query"
          clearable
        ></el-input>
        <slot name="left"></slot>
        <span fs-14 fw-400 class="c-[var(--g-text-color-1)]">共{{ totalRecords }}条数据</span>
      </div>
      <slot name="actions"></slot>
    </div>
    <ElAutoResizer flex-1 min-h-1>
      <template #default="{ height, width }">
        <ElTableV2
          ref="eltable2"
          :columns="tableColumns"
          :data="filteredData"
          :width="width"
          :height="height"
          :header-row-height="headerRowHeight"
          :row-height="rowHeight"
          :row-class="getRowClass"
          :row-event-handlers="rowEventHandlers"
          :sort-by="sortState"
          :fixed
          v-bind="$attrs"
          @column-sort="onSort"
        />
      </template>
    </ElAutoResizer>
  </div>
</template>
<style scoped>
.toolbar :deep(.el-button) {
  height: 36px;
  border-radius: 100px;
  --el-button-font-weight: 400;
}
</style>
